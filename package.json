{"name": "kylas-linkedin-extension", "version": "1.0.0", "description": "Chrome extension to extract LinkedIn contact information and sync with Kylas CRM", "main": "src/main.js", "scripts": {"build": "node scripts/build.js", "build:js": "esbuild src/background.js --bundle --minify --outfile=src/background.min.js --format=esm && esbuild src/content.js --bundle --minify --outfile=src/content.min.js --format=esm", "build:css": "mkdir -p src/bundle/ui/css && esbuild src/ui/css/toast.css src/ui/css/toggleBtn.css src/ui/css/home.css src/ui/css/sidebar.css --bundle --minify --outfile=src/bundle/ui/css/main.min.css", "copy:assets": "npm run copy:manifest && npm run copy:html && npm run copy:images && npm run copy:minified", "copy:manifest": "node scripts/update-manifest.js", "copy:html": "mkdir -p src/bundle/ui && cp -r src/ui/components src/bundle/ui/", "copy:images": "cp -r src/assets src/bundle/", "copy:minified": "cp src/background.min.js src/bundle/ && cp src/content.min.js src/bundle/", "package": "npm run build", "clean": "rm -rf src/bundle/* src/*.min.js src/bundle.zip 2>/dev/null || true", "dev": "npm run build", "watch": "npm run build && echo '👀 Use npm run build to rebuild after changes'", "install-extension": "echo '📋 Load the src/bundle folder in Chrome Extensions (Developer Mode)'", "help": "echo 'Available commands:\n  npm run build     - Build and package extension\n  npm run clean     - Clean build artifacts\n  npm run dev       - Development build\n  npm run help      - Show this help'"}, "keywords": ["chrome-extension", "linkedin", "crm", "kylas", "contact-extraction", "lead-management"], "author": "Kylas Team", "license": "MIT", "devDependencies": {"esbuild": "^0.19.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/kylas_linkedIn_scrapper.git"}, "bugs": {"url": "https://github.com/your-username/kylas_linkedIn_scrapper/issues"}, "homepage": "https://github.com/your-username/kylas_linkedIn_scrapper#readme"}