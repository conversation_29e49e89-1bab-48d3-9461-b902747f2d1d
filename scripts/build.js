#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Building Kylas LinkedIn Extension...\n');

// Clean previous builds
console.log('🧹 Cleaning previous builds...');
try {
  execSync('rm -rf src/bundle/* src/*.min.js src/bundle.zip', { stdio: 'inherit' });
} catch (error) {
  // Ignore errors if files don't exist
}

// Create bundle directory structure
console.log('📁 Creating bundle directory structure...');
const dirs = [
  'src/bundle',
  'src/bundle/ui',
  'src/bundle/ui/css',
  'src/bundle/ui/components',
  'src/bundle/assets'
];

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Build JavaScript files
console.log('⚙️  Building and minifying JavaScript...');
try {
  execSync('esbuild src/background.js --bundle --minify --outfile=src/background.min.js --format=esm', { stdio: 'inherit' });
  execSync('esbuild src/content.js --bundle --minify --outfile=src/content.min.js --format=esm', { stdio: 'inherit' });
  console.log('✅ JavaScript files built successfully');
} catch (error) {
  console.error('❌ Error building JavaScript files:', error.message);
  process.exit(1);
}

// Build CSS files
console.log('🎨 Building and minifying CSS...');
try {
  execSync('esbuild src/ui/css/toast.css src/ui/css/toggleBtn.css src/ui/css/home.css src/ui/css/sidebar.css --bundle --minify --outfile=src/bundle/ui/css/main.min.css', { stdio: 'inherit' });
  console.log('✅ CSS files built successfully');
} catch (error) {
  console.error('❌ Error building CSS files:', error.message);
  process.exit(1);
}

// Copy assets
console.log('📋 Copying assets...');

// Copy HTML components
try {
  execSync('cp -r src/ui/components/* src/bundle/ui/components/', { stdio: 'inherit' });
  console.log('✅ HTML components copied');
} catch (error) {
  console.error('❌ Error copying HTML components:', error.message);
  process.exit(1);
}

// Copy images and assets
try {
  execSync('cp -r src/assets/* src/bundle/assets/', { stdio: 'inherit' });
  console.log('✅ Assets copied');
} catch (error) {
  console.error('❌ Error copying assets:', error.message);
  process.exit(1);
}

// Copy minified JavaScript files
try {
  execSync('cp src/background.min.js src/bundle/', { stdio: 'inherit' });
  execSync('cp src/content.min.js src/bundle/', { stdio: 'inherit' });
  console.log('✅ Minified JavaScript files copied');
} catch (error) {
  console.error('❌ Error copying minified files:', error.message);
  process.exit(1);
}

// Update manifest for bundle
console.log('📄 Updating manifest for bundle...');
try {
  const sourceManifest = JSON.parse(fs.readFileSync('src/manifest.json', 'utf8'));
  
  const bundleManifest = {
    ...sourceManifest,
    content_scripts: [
      {
        ...sourceManifest.content_scripts[0],
        css: ["ui/css/main.min.css"]
      }
    ]
  };
  
  fs.writeFileSync('src/bundle/manifest.json', JSON.stringify(bundleManifest, null, 2));
  console.log('✅ Bundle manifest updated');
} catch (error) {
  console.error('❌ Error updating manifest:', error.message);
  process.exit(1);
}

// Create ZIP package
console.log('📦 Creating ZIP package...');
try {
  process.chdir('src/bundle');
  execSync('zip -r ../bundle.zip .', { stdio: 'inherit' });
  process.chdir('../..');
  console.log('✅ Extension packaged as src/bundle.zip');
} catch (error) {
  console.error('❌ Error creating ZIP package:', error.message);
  process.exit(1);
}

// Display build summary
console.log('\n🎉 Build completed successfully!');
console.log('\n📊 Build Summary:');
console.log('├── JavaScript files minified ✅');
console.log('├── CSS files bundled and minified ✅');
console.log('├── Assets copied ✅');
console.log('├── Manifest updated ✅');
console.log('└── ZIP package created ✅');

console.log('\n📁 Output files:');
console.log('├── src/bundle/ (Chrome extension folder)');
console.log('└── src/bundle.zip (Distribution package)');

console.log('\n🚀 Next steps:');
console.log('1. Load src/bundle folder in Chrome Extensions (Developer Mode)');
console.log('2. Or upload src/bundle.zip to Chrome Web Store');

// Display file sizes
try {
  const bundleStats = fs.statSync('src/bundle.zip');
  const backgroundStats = fs.statSync('src/bundle/background.min.js');
  const contentStats = fs.statSync('src/bundle/content.min.js');
  const cssStats = fs.statSync('src/bundle/ui/css/main.min.css');
  
  console.log('\n📏 File sizes:');
  console.log(`├── bundle.zip: ${(bundleStats.size / 1024).toFixed(2)} KB`);
  console.log(`├── background.min.js: ${(backgroundStats.size / 1024).toFixed(2)} KB`);
  console.log(`├── content.min.js: ${(contentStats.size / 1024).toFixed(2)} KB`);
  console.log(`└── main.min.css: ${(cssStats.size / 1024).toFixed(2)} KB`);
} catch (error) {
  // Ignore file size errors
}
