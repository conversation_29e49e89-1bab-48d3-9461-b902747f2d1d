#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the source manifest
const sourceManifest = JSON.parse(fs.readFileSync('src/manifest.json', 'utf8'));

// Update the manifest for the bundle version
const bundleManifest = {
  ...sourceManifest,
  content_scripts: [
    {
      ...sourceManifest.content_scripts[0],
      css: ["ui/css/main.min.css"] // Use the minified CSS bundle
    }
  ]
};

// Ensure the bundle directory exists
const bundleDir = 'src/bundle';
if (!fs.existsSync(bundleDir)) {
  fs.mkdirSync(bundleDir, { recursive: true });
}

// Write the updated manifest
fs.writeFileSync(
  path.join(bundleDir, 'manifest.json'), 
  JSON.stringify(bundleManifest, null, 2)
);

console.log('✅ Bundle manifest.json updated successfully');
