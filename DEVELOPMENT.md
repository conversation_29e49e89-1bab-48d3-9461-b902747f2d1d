# Development Guide

This guide provides detailed instructions for developers working on the Kylas LinkedIn Extension.

## 🛠️ Development Setup

### Prerequisites

- **Node.js** (v16 or higher)
- **npm** (v8 or higher)
- **Google Chrome** (latest version)
- **Git** for version control

### Initial Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/kylas_linkedIn_scrapper.git
   cd kylas_linkedIn_scrapper
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the extension**:
   ```bash
   npm run build
   ```

## 🏗️ Build System

The project uses **esbuild** for fast, efficient bundling and minification.

### Build Commands

```bash
# Full build and package
npm run build

# Individual build steps
npm run build:js      # Build JavaScript files only
npm run build:css     # Build CSS files only
npm run copy:assets   # Copy assets only

# Development
npm run dev           # Development build
npm run clean         # Clean build artifacts
npm run help          # Show available commands
```

### Build Process

1. **Clean**: Removes previous build artifacts
2. **JavaScript**: Bundles and minifies `background.js` and `content.js`
3. **CSS**: Combines and minifies all CSS files into `main.min.css`
4. **Assets**: Copies HTML components, images, and other assets
5. **Manifest**: Updates manifest.json for the bundle version
6. **Package**: Creates a ZIP file ready for Chrome Web Store

### Output Structure

```
src/bundle/
├── manifest.json           # Updated manifest for production
├── background.min.js       # Minified service worker
├── content.min.js          # Minified content script
├── assets/                 # Images and icons
│   ├── kylas.png
│   ├── minimize.svg
│   ├── logout.svg
│   └── info-circle.svg
└── ui/
    ├── components/         # HTML templates
    │   ├── home.html
    │   ├── sidebar.html
    │   └── toggleBtn.html
    └── css/
        └── main.min.css    # Bundled and minified CSS
```

## 🧪 Testing

### Manual Testing

1. **Load Extension in Chrome**:
   ```bash
   # After building
   npm run install-extension
   ```
   Then load the `src/bundle` folder in Chrome Extensions (Developer Mode)

2. **Test on LinkedIn**:
   - Navigate to any LinkedIn profile
   - Click "Contact info" on the profile
   - Test the extension functionality

### Testing Checklist

- [ ] Extension loads without errors
- [ ] Login functionality works
- [ ] Contact extraction works on various LinkedIn profiles
- [ ] Lead creation in Kylas CRM
- [ ] Lead search and update functionality
- [ ] Error handling and user feedback
- [ ] UI responsiveness across different screen sizes

## 🔧 Development Workflow

### File Structure

```
src/
├── api/                    # API integration modules
│   ├── createLeadInKylas.js
│   ├── fetchLeadFields.js
│   ├── fetchLeadLabels.js
│   ├── loginToKylas.js
│   ├── logoutFromKylas.js
│   ├── searchLeadInKylas.js
│   └── updateLeadInKylas.js
├── assets/                 # Static assets
├── ui/                     # User interface
│   ├── components/         # HTML templates
│   └── css/               # Stylesheets
├── utils/                  # Utility functions
├── background.js           # Service worker
├── content.js              # Content script
├── config.js               # Configuration
├── main.js                 # Entry point
└── manifest.json           # Extension manifest
```

### Key Files

- **`manifest.json`**: Extension configuration and permissions
- **`background.js`**: Service worker handling API calls
- **`content.js`**: Content script for LinkedIn integration
- **`config.js`**: API endpoints and configuration
- **`api/`**: Modular API integration functions
- **`ui/`**: User interface components and styles

### Making Changes

1. **Edit source files** in the `src/` directory
2. **Run build** to update the bundle:
   ```bash
   npm run build
   ```
3. **Reload extension** in Chrome Extensions page
4. **Test changes** on LinkedIn

### Debugging

1. **Chrome DevTools**:
   - Right-click extension icon → "Inspect popup"
   - Check Console for errors
   - Use Network tab for API debugging

2. **Content Script Debugging**:
   - Open DevTools on LinkedIn page
   - Check Console for content script errors
   - Use Sources tab to debug content script

3. **Service Worker Debugging**:
   - Go to `chrome://extensions/`
   - Click "Inspect views: service worker"
   - Debug background script

## 🎨 UI Development

### CSS Architecture

- **Modular CSS**: Each component has its own CSS file
- **Build Process**: All CSS files are bundled into `main.min.css`
- **Responsive Design**: Uses flexible layouts and media queries

### Component Structure

- **`home.html`**: Main extension interface
- **`sidebar.html`**: Login interface
- **`toggleBtn.html`**: Toggle button component

### Styling Guidelines

- Use the Rubik font family
- Follow existing color scheme and spacing
- Ensure accessibility (contrast, focus states)
- Test across different screen sizes

## 🔌 API Integration

### Kylas CRM API

The extension integrates with Kylas CRM through REST APIs:

- **Authentication**: JWT token-based
- **Endpoints**: Defined in `config.js`
- **Error Handling**: Centralized error code mapping

### API Modules

Each API operation has its own module in `src/api/`:

- **`loginToKylas.js`**: User authentication
- **`createLeadInKylas.js`**: Create new leads
- **`updateLeadInKylas.js`**: Update existing leads
- **`searchLeadInKylas.js`**: Search for leads
- **`fetchLeadFields.js`**: Get CRM field configuration
- **`fetchLeadLabels.js`**: Get CRM terminology

### Adding New API Endpoints

1. Create a new file in `src/api/`
2. Follow the existing pattern for error handling
3. Import and use in `background.js`
4. Add message handling in the service worker

## 🚀 Deployment

### Chrome Web Store

1. **Build the extension**:
   ```bash
   npm run build
   ```

2. **Upload to Chrome Web Store**:
   - Use the generated `src/bundle.zip` file
   - Follow Chrome Web Store developer guidelines

### Version Management

- Update version in `manifest.json`
- Document changes in version notes
- Test thoroughly before release

## 🐛 Common Issues

### Build Issues

- **esbuild not found**: Run `npm install`
- **Permission errors**: Check file permissions
- **Missing directories**: Build script creates them automatically

### Extension Issues

- **Extension not loading**: Check manifest.json syntax
- **API errors**: Verify Kylas CRM credentials and endpoints
- **Content script not working**: Check LinkedIn page structure changes

### Development Tips

- Use `console.log()` for debugging
- Check Chrome Extensions page for error messages
- Test with different LinkedIn profiles
- Verify API responses in Network tab

## 📚 Resources

- [Chrome Extension Documentation](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 Migration Guide](https://developer.chrome.com/docs/extensions/mv3/intro/)
- [esbuild Documentation](https://esbuild.github.io/)
- [Kylas CRM API Documentation](https://api.kylas.io/docs)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Code Style

- Use consistent indentation (2 spaces)
- Add comments for complex logic
- Follow existing naming conventions
- Keep functions small and focused
