const fetchHTML = async (path) => {
  try {
    const response = await fetch(chrome.runtime.getURL(path));
    return await response.text();
  } catch (error) {
    console.error(`Error loading ${path}:`, error);
    return "";
  }
};

const setImageSource = (element, selector, imagePath) => {
  const imgElement = element.querySelector(selector);
  if (imgElement) {
    imgElement.src = chrome.runtime.getURL(imagePath);
  }
};

const showToast = (message, type = "success") => {
  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.innerHTML = message;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.classList.add("fade-out");
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 4000);
};

const createToggleSidebarContainer = async () => {
  const toggleSidebarContainer = document.createElement("div");
  toggleSidebarContainer.id = "toggleSidebarContainer";
  toggleSidebarContainer.style.right = "0px";
  toggleSidebarContainer.innerHTML = await fetchHTML(
    "ui/components/toggleBtn.html"
  );

  setImageSource(toggleSidebarContainer, "#kylas-logo", "assets/kylas.png");

  document.body.appendChild(toggleSidebarContainer);
  return toggleSidebarContainer;
};

const createSidebarContainer = async () => {
  const sidebarContainer = document.createElement("div");
  sidebarContainer.id = "sidebarContainer";
  sidebarContainer.style.right = "-320px";
  sidebarContainer.innerHTML = await fetchHTML("ui/components/sidebar.html");

  setImageSource(sidebarContainer, "#minimize", "assets/minimize.svg");
  setImageSource(sidebarContainer, "#logout", "assets/logout.svg");

  document.body.appendChild(sidebarContainer);
  return sidebarContainer;
};

const extractContactInfo = async () => {
  await new Promise((resolve) => setTimeout(resolve, 3000));

  const modal = document.querySelector('[aria-labelledby="pv-contact-info"]');
  if (!modal || modal.style.display === "none") return null;

  const modalText = modal.innerText;
  const lines = modalText
    .split("\n")
    ?.map((line) => line.trim())
    .filter(Boolean);

  const name = lines[1].trim().split(" ") || "";
  const firstName = name[0];
  const lastName = name[name.length - 1];

  const linkedInUrl = window.location.href.replace(
    "/overlay/contact-info/",
    ""
  );

  const emailIndex = lines.indexOf("Email");
  const email =
    emailIndex >= 0 && lines[emailIndex + 1] ? lines[emailIndex + 1] : "";

  const phoneMatch = modalText.match(/Phone\s+(.+?)\s+\(Mobile\)/);
  const phoneNumber = phoneMatch ? phoneMatch[1].trim() : "";

  let companyName =
    document
      .querySelector('button[aria-label^="Current company:"]')
      ?.getAttribute("aria-label")
      ?.split(":")[1]
      ?.replace(". Click to skip to experience card", "")
      ?.trim() || "";

  return { firstName, lastName, email, phoneNumber, linkedInUrl, companyName };
};

const toggleSidebarListener = (sidebarContainer, toggleSidebarContainer) => {
  toggleSidebarContainer.addEventListener("click", () => {
    const isExpanded = sidebarContainer.style.right === "0px";
    sidebarContainer.style.right = isExpanded ? "-320px" : "0px";
  });
};

const minimizeBtnListener = () => {
  document.getElementById("minimizeBtn")?.addEventListener("click", () => {
    document.getElementById("sidebarContainer").style.right = "-320px";
    document.getElementById("toggleSidebarContainer").style.right = "0px";
  });
};

const setLoadingInDropdown = () => {
  const updateSearchResults = document.getElementById("updateSearchResults");
  updateSearchResults.innerHTML =
    '<div class="no-results"><img src="https://assets.kylas.io/images/webloader.gif" alt="Loading..." class="kl-loading-gif"/div>';
  document.getElementById("updateSearchResults").style.display = "block";
};

const setNoMatchingResults = async () => {
  const updateSearchResults = document.getElementById("updateSearchResults");
  const leadDisplayNames = await getLeadDisplayName(true); // Get plural form
  const leadDisplayName = await getLeadDisplayName(false); // Get sigular form

  updateSearchResults.innerHTML = `
    <div class="no-results">
      <div>No matching ${leadDisplayNames}</div>
      <button class="create-new-btn">Create New ${leadDisplayName}</button>
    </div>
  `;
  updateSearchResults.style.display = "block";

  toggleUpdateFormBlur(true);

  // Add click handler for the new button
  const createNewBtn = updateSearchResults.querySelector('.create-new-btn');
  createNewBtn?.addEventListener('click', () => {
    const createLeadRadioBtn = document.getElementById('createLeadRadioBtn');
    if (createLeadRadioBtn) {
      createLeadRadioBtn.checked = true;
      // Trigger the change event to activate the form switch
      createLeadRadioBtn.dispatchEvent(new Event('change'));
      toggleUpdateFormBlur(false);
    }
    // toggleUpdateFormBlur(false);
  });
};

const toggleUpdateFormBlur = (shouldBlur) => {
  const updateLeadFormLastName = document.getElementById("updateLeadFormLastName");
  const updateLeadFormEmail = document.getElementById("updateLeadFormEmail");
  const updateLeadFormPhoneNumber = document.getElementById("updateLeadFormPhoneNumber");
  const updateLeadFormLinkedInURL = document.getElementById("updateLeadFormLinkedInURL");
  const updateLeadFormCompanyName = document.getElementById("updateLeadFormCompanyName");
  const updateLeadId = document.getElementById("updateLeadId");
  const updateLeadButton = document.getElementById("updateLeadBtn");

  // If updateLeadId has a value, remove blur regardless of shouldBlur parameter
  if (updateLeadId && updateLeadId.value) {
    updateLeadFormLastName.classList.remove('blur-container');
    updateLeadFormEmail.classList.remove('blur-container');
    updateLeadFormPhoneNumber.classList.remove('blur-container');
    updateLeadFormLinkedInURL.classList.remove('blur-container');
    updateLeadFormCompanyName.classList.remove('blur-container');
    updateLeadButton.classList.remove('blur-container');
  } else {
    // Otherwise use the shouldBlur parameter
    if (shouldBlur) {
      updateLeadFormLastName.classList.add('blur-container');
      updateLeadFormEmail.classList.add('blur-container');
      updateLeadFormPhoneNumber.classList.add('blur-container');
      updateLeadFormLinkedInURL.classList.add('blur-container');
      updateLeadFormCompanyName.classList.add('blur-container');
      updateLeadButton.classList.add('blur-container');
    } else {
      updateLeadFormLastName.classList.remove('blur-container');
      updateLeadFormEmail.classList.remove('blur-container');
      updateLeadFormPhoneNumber.classList.remove('blur-container');
      updateLeadFormLinkedInURL.classList.remove('blur-container');
      updateLeadFormCompanyName.classList.remove('blur-container');
      updateLeadButton.classList.remove('blur-container');
    }
  }
};

const profilePageListener = () => {
  if (window.location.href.includes("/in/")) {
    const observer = new MutationObserver((mutations, obs) => {
      const contactInfoBtn = document.getElementById(
        "top-card-text-details-contact-info"
      );
      if (contactInfoBtn) {
        contactInfoBtn.addEventListener("click", async () => {
          const {
            firstName,
            lastName,
            email,
            phoneNumber,
            linkedInUrl,
            companyName,
          } = await extractContactInfo();

          document.getElementById("firstName").value = firstName;
          document.getElementById("lastName").value = lastName;
          document.getElementById("email").value = email;
          document.getElementById("phoneNumber").value = phoneNumber;
          document.getElementById("linkedInUrl").value = linkedInUrl;
          document.getElementById("companyName").value = companyName;

          const updateLeadRadioBtn =
            document.getElementById("updateLeadRadioBtn");
          leadSearchHandler();
          document.getElementById("updateLeadId").value = null;
          document.getElementById("updateFirstName").value = null;
          document.getElementById("updateLastName").value = null;
          document.getElementById("updateEmail").value = null;
          document.getElementById("updatePhoneNumber").value = null;
          document.getElementById("updateLinkedInUrl").value = null;
          document.getElementById("updateCompanyName").value = null;
        });
        obs.disconnect();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }
};

const setupEventListeners = (sidebarContainer, toggleSidebarContainer) => {
  toggleSidebarListener(sidebarContainer, toggleSidebarContainer);
  minimizeBtnListener();
};

const updateFormLabelsDisplayText = (fields) => {
  // Define field mapping
  const fieldNameMap = {
    firstName: ["firstName", "updateFirstName"],
    lastName: ["lastName", "updateLastName"],
    emails: ["email", "updateEmail"],
    phoneNumbers: ["phoneNumber", "updatePhoneNumber"],
    linkedIn: ["linkedInUrl", "updateLinkedInUrl"],
    companyName: ["companyName", "updateCompanyName"],
  };

  fields.forEach((field) => {
    if (fieldNameMap[field.name]) {
      // Get the IDs for both create and update form fields
      const [createId, updateId] = fieldNameMap[field.name];

      // Update labels in create form
      const createInput = document.getElementById(createId);
      if (createInput) {
        const createLabel =
          createInput.parentElement.querySelector(".field-label");
        if (createLabel) {
          createLabel.textContent = field.displayName;
        }
      }

      // Update labels in update form
      const updateInput = document.getElementById(updateId);
      if (updateInput) {
        const updateLabel =
          updateInput.parentElement.querySelector(".field-label");
        if (updateLabel) {
          updateLabel.textContent = field.displayName;
        }
      }
    }
  });
};

const getLeadDisplayName = async (isPlural = false) => {
  const leadLabels = await chrome.storage.local.get(["leadLabels"]);
  if (leadLabels.leadLabels) {
    return isPlural ? leadLabels.leadLabels.displayNamePlural : leadLabels.leadLabels.displayName;
  }
  return isPlural ? "Leads" : "Lead"; // Fallback
};

const updateButtonLabels = async () => {
  const leadName = await getLeadDisplayName();

  // for create button
  const createBtn = document.getElementById("createLeadBtn");
  if (createBtn) {
    createBtn.textContent = `Create ${leadName}`;
  }

  // for update button
  const updateBtn = document.getElementById("updateLeadBtn");
  if (updateBtn) {
    updateBtn.textContent = `Update ${leadName}`;
  }
};

const initializePhoneNumberTooltip = async () => {
  const tooltipIcon = document.getElementById("phoneNumberTooltipIcon");
  const updateFormTooltipIcon = document.getElementById("updatephoneNumberTooltipIcon");
  const tooltip = document.getElementById("phoneNumberTooltip");
  const updateTooltip = document.getElementById("updatePhoneNumberTooltip");

  // Fetch the dynamic phone number value
  const leadFieldsResponse = await chrome.runtime.sendMessage({ event: "fetchLeadFields" });
  let phoneNumber = "the phone number"; // Default text
  if (leadFieldsResponse.success) {
    const phoneField = leadFieldsResponse.data.find((field) => field.name === "phoneNumbers");
    if (phoneField && phoneField.displayName) {
      phoneNumber = phoneField.displayName;
    }
  }

  // Set the tooltip text dynamically
  tooltip.textContent = `Please enter ${phoneNumber} along with country code`;

  // Show tooltip on hover
  tooltipIcon.addEventListener("mouseenter", () => {
    tooltip.style.display = "block";
    tooltip.style.top = `${tooltipIcon.offsetTop + tooltipIcon.offsetHeight + 5}px`;
    tooltip.style.left = `${tooltipIcon.offsetLeft}px`;
  });

  // Hide tooltip when not hovering
  tooltipIcon.addEventListener("mouseleave", () => {
    tooltip.style.display = "none";
  });

  // Show tooltip on hover
  updateFormTooltipIcon.addEventListener("mouseenter", () => {
    updateTooltip.style.display = "block";
    updateTooltip.style.top = `${updateFormTooltipIcon.offsetTop + updateFormTooltipIcon.offsetHeight + 5}px`;
    updateTooltip.style.left = `${updateFormTooltipIcon.offsetLeft}px`;
  });

  // Hide tooltip when not hovering
  updateFormTooltipIcon.addEventListener("mouseleave", () => {
    updateTooltip.style.display = "none";
  });
};

const setupHomePage = async () => {
  const homeHTML = await fetchHTML("ui/components/home.html");
  const sidebar = document.getElementById("sidebarContainer");
  sidebar.innerHTML = homeHTML;

  chrome.runtime.sendMessage({ event: "fetchLeadFields" }, async (response) => {
    if (response.success) {
      const fields = response.data;
      await updateButtonLabels();
      updateFormLabelsDisplayText(fields);
    } else if (
      response.code &&
      (response.code === "001002" ||
        response.code === "001003" ||
        response.code === "001004")
    ) {
      await setupToLogout();
      showToast("Session expired or invalid. Please log in again.", "warning");
    } else {
      const errorMessage = await getErrorMessage(response.code);
      showToast(errorMessage, "error");
    }
  });

  const emailResponse = await chrome.storage.local.get(["email"]);
  const email = emailResponse.email;
  const userEmail = document.getElementById("userEmail");
  userEmail.textContent = email;

  setImageSource(sidebar, "#minimize", "assets/minimize.svg");
  setImageSource(sidebar, "#logout", "assets/logout.svg");
  setImageSource(sidebar, "#phoneNumberTooltipIcon", "assets/info-circle.svg");
  setImageSource(sidebar, "#updatephoneNumberTooltipIcon", "assets/info-circle.svg");


  setupHomeEventListeners();
  initializePhoneNumberTooltip();
};

const fetchErrorCodeMessages = async () => {
  // Check if messages are already stored in Chrome's local storage
  const cachedMessages = await chrome.storage.local.get("errorCodeMessages");
  if (cachedMessages.errorCodeMessages) {
    return cachedMessages.errorCodeMessages; // Return cached messages
  }

  try {
    // Fetch messages from the CDN
    const response = await fetch("https://assets.kylas.io/chrome-extention/errorcode-messages.json");
    if (!response.ok) {
      throw new Error("Failed to fetch error code messages");
    }
    const messages = await response.json();

    // Cache the messages in Chrome's local storage
    await chrome.storage.local.set({ errorCodeMessages: messages });

    return messages;
  } catch (error) {
    console.error("Error fetching error code messages:", error);
    return {}; // Return an empty object if fetching fails
  }
};

const getErrorMessage = async (code) => {
  const messages = await fetchErrorCodeMessages();
  return messages[code] || "Something went wrong. Please try again."; // Default message
};

const loginHandler = async () => {
  await fetchErrorCodeMessages();
  document.getElementById("loginBtn")?.addEventListener("click", async () => {
    const email = document.getElementById("email").value;
    const password = document.getElementById("password").value;

    if (!email) {
      showToast("Email is required", "error");
      return;
    }

    if (!validateEmail(email)) {
      showToast("Please enter a valid email address", "error");
      return;
    }

    if (!password) {
      showToast("Password is required", "error");
      return;
    }

    chrome.runtime.sendMessage(
      {
        event: "login",
        data: { payload: { email, password, rememberMe: true } },
      },
      async (response) => {
        if (response.success) {
          chrome.storage.local.set({ token: response.data.token });
          chrome.storage.local.set({ email });

          // Fetch lead labels after successful login
          chrome.runtime.sendMessage({ event: "fetchLeadLabels" }, (labelsResponse) => {
            if (!labelsResponse.success) {
              console.error("Failed to fetch lead labels:", labelsResponse.message);
            }
          });
          showToast(response.message, "success");
          setupHomePage();
        } else {
          const errorMessage = await getErrorMessage(response.code);
          showToast(errorMessage, "error");
        }
      }
    );
  });
  emailListener();
};

const setupToLogout = async () => {
  chrome.storage.local.remove("token");
  chrome.storage.local.remove("email");
  chrome.storage.local.remove("leadLabels");
  chrome.storage.local.remove("leadFields");
  chrome.storage.local.remove("errorCodeMessages");

  const sidebarContainer = document.getElementById("sidebarContainer");
  const toggleSidebarContainer = document.getElementById(
    "toggleSidebarContainer"
  );

  sidebarContainer.innerHTML = await fetchHTML("ui/components/sidebar.html");

  setImageSource(sidebarContainer, "#minimize", "assets/minimize.svg");
  setupEventListeners(sidebarContainer, toggleSidebarContainer);
  toggleSidebarListener(sidebarContainer, toggleSidebarContainer);
  loginHandler();
};

const logoutHandler = async () => {
  document.getElementById("logoutBtn")?.addEventListener("click", () => {
    chrome.runtime.sendMessage(
      { event: "logout", data: {} },
      async (response) => {
        showToast(response.message, "success");
        await setupToLogout();
      }
    );
  });
};

const watchContactInfoButton = () => {
  const observer = new MutationObserver((mutations) => {
    const contactInfoBtn = document.getElementById("top-card-text-details-contact-info");

    if (contactInfoBtn && !contactInfoBtn.hasAttribute('listener-attached')) {
      contactInfoBtn.setAttribute('listener-attached', 'true');

      contactInfoBtn.addEventListener("click", () => {
        // Make sure update form is visible
        const updateForm = document.getElementById("updateLeadForm");
        const updateRadioBtn = document.getElementById("updateLeadRadioBtn");

        // Select update radio button to show update form
        if (updateRadioBtn) {
          updateRadioBtn.checked = true;
          updateRadioBtn.dispatchEvent(new Event('change')); // This triggers form display change
        }

        // Now that form is visible, show loading
        setTimeout(() => {
          setLoadingInDropdown();
        }, 100); // Small delay to ensure form is visible
      });
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  return () => observer.disconnect();
};

const radioBtnListener = () => {
  const radioButtons = document.querySelectorAll('input[name="leadAction"]');
  const createForm = document.getElementById("createLeadForm");
  const updateForm = document.getElementById("updateLeadForm");

  radioButtons.forEach((radio) => {
    radio.addEventListener("change", (e) => {
      if (e.target.value === "create") {
        createForm.style.display = "block";
        updateForm.style.display = "none";
      } else {
        setLoadingInDropdown();
        leadSearchHandler();
        createForm.style.display = "none";
        updateForm.style.display = "block";
      }
    });
  });
};

const profileHandler = () => {
  const profileBtn = document.getElementById("profileBtn");
  const profileDropdown = document.getElementById("profileDropdown");
  const userEmail = document.getElementById("userEmail");
  const profileCircle = document.querySelector(".profile-circle");
  const profileCircleSmall = document.querySelector(".profile-circle-small");

  if (profileCircle && profileCircleSmall && userEmail) {
    const email = userEmail.textContent;
    const initial = email.charAt(0).toUpperCase();
    profileCircle.textContent = initial;
    profileCircleSmall.textContent = initial;
  }

  profileBtn?.addEventListener("click", (event) => {
    event.stopPropagation();
    profileDropdown.style.display =
      profileDropdown.style.display === "block" ? "none" : "block";
  });

  document.addEventListener("click", (event) => {
    if (
      !profileBtn?.contains(event.target) &&
      !profileDropdown?.contains(event.target)
    ) {
      profileDropdown.style.display = "none";
      toggleUpdateFormBlur(false);
    }
  });
};

let emailFromKylasBackend = "";
let phoneNumberFromKylasBackend = "";

const leadSearchHandler = () => {
  let searchTimeout;
  const firstNameInput = document.getElementById("firstName");
  const emailInput = document.getElementById("email");
  const phoneNumberInput = document.getElementById("phoneNumber");
  const updateFirstNameInput = document.getElementById("updateFirstName");
  const updateSearchResults = document.getElementById("updateSearchResults");

  const updateExistingBtn = document.getElementById("updateLeadRadioBtn");

  const handleSearch = () => {
    let searchText = "";
    if (updateFirstNameInput.value.trim() !== "") {
      searchText = updateFirstNameInput.value.trim();
    } else if (emailInput.value && emailInput.value.trim() !== "") {
      searchText = emailInput.value.trim();
    } else if (phoneNumberInput.value && phoneNumberInput.value.trim() !== "") {
      searchText = phoneNumberInput.value.trim();
    } else {
      searchText = firstNameInput.value.trim();
    }

    clearTimeout(searchTimeout);

    if (searchText.length <= 3) {
      updateSearchResults.style.display = "none";
      return;
    }

    searchTimeout = setTimeout(() => {
      setLoadingInDropdown();
      chrome.runtime.sendMessage(
        {
          event: "searchLead",
          data: {
            payload: {
              fields: [
                "firstName",
                "lastName",
                "id",
                "emails",
                "phoneNumbers",
                "linkedIn",
                "companyName",
              ],
              jsonRule: {
                rules: [
                  {
                    id: "multi_field",
                    field: "multi_field",
                    type: "multi_field",
                    input: "multi_field",
                    operator: "multi_field",
                    value: searchText,
                  },
                ],
                condition: "AND",
                valid: true,
              },
            },
          },
        },
        async (response) => {
          const updateSearchResults = document.getElementById(
            "updateSearchResults"
          );
          if (response.success && response.data?.content?.length > 0) {
            updateSearchResults.innerHTML = response.data.content
              .map(
                (lead) => `
                  <div class="search-result-item" data-lead-id="${lead.id}">
                    <div class="lead-name">${lead.firstName ? lead.firstName : ""
                  } ${lead.lastName ? lead.lastName : ""} (#${lead.id})</div>
                    <div class="lead-details">
                      ${lead.emails?.[0]?.value || "No email"}
                    </div>
                  </div>
                `
              )
              .join("");

            const resultItems = updateSearchResults.querySelectorAll(
              ".search-result-item"
            );
            resultItems.forEach((item) => {
              item.addEventListener("click", () => {
                const selectedLead = response.data.content.find(
                  (lead) => lead.id === parseInt(item.dataset.leadId)
                );
                if (selectedLead) {
                  emailFromKylasBackend = selectedLead.emails
                    ? selectedLead?.emails[0]?.value
                    : "";
                  phoneNumberFromKylasBackend = selectedLead.phoneNumbers
                    ? selectedLead?.phoneNumbers[0]?.value
                    : "";
                  document.getElementById("updateLeadId").value =
                    selectedLead.id;

                  if (selectedLead.firstName) {
                    document.getElementById("updateFirstName").value =
                      selectedLead.firstName;
                  } else {
                    document.getElementById("updateFirstName").value =
                      document.getElementById("firstName").value;
                  }

                  document.getElementById("updateLastName").value =
                    selectedLead.lastName || "";

                  if (selectedLead?.emails) {
                    document.getElementById("updateEmail").value =
                      selectedLead?.emails[0]?.value;
                  } else {
                    document.getElementById("updateEmail").value =
                      document.getElementById("email").value;
                  }

                  if (selectedLead?.phoneNumbers) {
                    document.getElementById("updatePhoneNumber").value =
                      selectedLead?.phoneNumbers[0]?.value;
                  } else {
                    document.getElementById("updatePhoneNumber").value =
                      document.getElementById("phoneNumber").value;
                  }

                  if (document.getElementById("linkedInUrl").value) {
                    document.getElementById("updateLinkedInUrl").value =
                      document.getElementById("linkedInUrl").value;
                  } else {
                    document.getElementById("updateLinkedInUrl").value =
                      selectedLead.linkedIn;
                  }

                  if (selectedLead.companyName) {
                    document.getElementById("updateCompanyName").value =
                      selectedLead.companyName;
                  } else {
                    document.getElementById("updateCompanyName").value =
                      document.getElementById("companyName").value;
                  }

                  updateSearchResults.style.display = "none";

                  document.addEventListener("click", (e) => {
                    updateSearchResults.style.display = "none";
                  });

                  let nullFieldsToasterText = "";
                  if (document.getElementById("updateFirstName").value === "") {
                    nullFieldsToasterText += "First Name, ";
                  }
                  if (document.getElementById("updateLastName").value === "") {
                    nullFieldsToasterText += "Last Name, ";
                  }
                  if (document.getElementById("updateEmail").value === "") {
                    nullFieldsToasterText += "Email, ";
                  }
                  if (
                    document.getElementById("updatePhoneNumber").value === ""
                  ) {
                    nullFieldsToasterText += "Phone Number, ";
                  }
                  if (
                    document.getElementById("updateLinkedInUrl").value === ""
                  ) {
                    nullFieldsToasterText += "LinkedIn URL, ";
                  }
                  if (
                    document.getElementById("updateCompanyName").value === ""
                  ) {
                    nullFieldsToasterText += "Company Name, ";
                  }
                  if (nullFieldsToasterText !== "") {
                    nullFieldsToasterText +=
                      "These fields are not available in Kylas & LinkedIn";

                    showToast(nullFieldsToasterText, "warning");
                  }
                }
              });
            });
          } else if (
            response.code &&
            (response.code === "001002" ||
              response.code === "001003" ||
              response.code === "001004")
          ) {
            console.log(response);
            await setupToLogout();
            showToast(
              "Session expired or invalid. Please log in again.",
              "warning"
            );
          } else {
            setNoMatchingResults();
          }
        }
      );
    }, 300);
  };

  updateExistingBtn?.addEventListener("click", handleSearch);
  updateFirstNameInput?.addEventListener("input", handleSearch);
  handleSearch();
};

const validateEmail = (email) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

const emailListener = () => {
  ['email', 'updateEmail'].forEach(id => {
    const emailInput = document.getElementById(id);
    emailInput?.addEventListener('input', (e) => {
      const email = e.target.value;
      if (email) {
        if (!validateEmail(email)) {
          e.target.classList.add('invalid-input');
        } else {
          e.target.classList.remove('invalid-input');
        }
      } else {
        e.target.classList.remove('invalid-input');
      }
    });
  });
};

const createLeadListener = () => {
  document
    .getElementById("createLeadBtn")
    ?.addEventListener("click", async () => {
      const tokenResponse = await chrome.storage.local.get(["token"]);
      if (!tokenResponse.token) {
        logoutHandler();
        return;
      }

      const firstName = document.getElementById("firstName").value;
      const lastName = document.getElementById("lastName").value;
      const email = document.getElementById("email").value;
      const phoneNumber = document.getElementById("phoneNumber").value.trim();
      const linkedInUrl = document.getElementById("linkedInUrl").value;
      const companyName = document.getElementById("companyName").value;

      if (email && !validateEmail(email)) {
        showToast("Please enter a valid email address", "error");
        return;
      }

      if (!firstName.trim() && !lastName.trim()) {
        showToast("Can't create lead", "error");
        return;
      }

      const hasCountryCode = phoneNumber.startsWith("+");
      const phonePayload = phoneNumber
        ? [
          {
            type: "MOBILE",
            code: hasCountryCode ? null : "IN",
            value: phoneNumber,
            dialCode: hasCountryCode ? null : "+91",
            primary: true,
          },
        ]
        : null;

      chrome.runtime.sendMessage(
        {
          event: "createLead",
          data: {
            payload: {
              firstName: firstName,
              lastName: lastName,
              emails: email
                ? [
                  {
                    type: "PERSONAL",
                    value: email,
                    primary: true,
                  },
                ]
                : null,
              phoneNumbers: phonePayload,
              linkedIn: linkedInUrl,
              companyName: companyName,
              utmSource: "LinkedIn",
            },
          },
        },
        async (response) => {
          console.log(response);
          if (response.success) {
            document.getElementById("firstName").value = null;
            document.getElementById("lastName").value = null;
            document.getElementById("email").value = null;
            document.getElementById("phoneNumber").value = null;
            document.getElementById("linkedInUrl").value = null;
            document.getElementById("companyName").value = null;
            showToast(response.message, "success");
          } else if (
            response.code &&
            (response.code === "001002" ||
              response.code === "001003" ||
              response.code === "001004")
          ) {
            await setupToLogout();
            showToast(
              "Session expired or invalid. Please log in again.",
              "warning"
            );
          } else {
            const errorMessage = await getErrorMessage(response.code);
            showToast(errorMessage, "error");
          }
        }
      );
    });
};

const updateLeadListener = () => {
  document
    .getElementById("updateLeadBtn")
    ?.addEventListener("click", async () => {
      const tokenResponse = await chrome.storage.local.get(["token"]);
      if (!tokenResponse.token) {
        logoutHandler();
        return;
      }

      const leadId = document.getElementById("updateLeadId").value;
      const firstName = document.getElementById("updateFirstName").value;
      const lastName = document.getElementById("updateLastName").value;
      const email = document.getElementById("updateEmail").value;
      const phoneNumber = document.getElementById("updatePhoneNumber").value.trim();
      const linkedInUrl = document.getElementById("updateLinkedInUrl").value;
      const companyName = document.getElementById("updateCompanyName").value;

      if (email && !validateEmail(email)) {
        showToast("Please enter a valid email address", "error");
        return;
      }

      if (!leadId) {
        showToast("No lead selected", "error");
        return;
      }

      const payload = [];
      if (firstName) {
        payload.push({
          op: "add",
          path: "/firstName",
          value: firstName,
        });
      }
      if (lastName) {
        payload.push({
          op: "add",
          path: "/lastName",
          value: lastName,
        });
      }
      if (email && email != emailFromKylasBackend) {
        payload.push({
          op: "add",
          path: "/emails/0",
          value: {
            type: "PERSONAL",
            value: email,
            primary: false,
          },
        });
      }
      const hasCountryCode = phoneNumber.startsWith("+");
      if (phoneNumber && phoneNumber != phoneNumberFromKylasBackend) {
        payload.push({
          op: "add",
          path: "/phoneNumbers/0",
          value: {
            id: null,
            type: "MOBILE",
            code: hasCountryCode ? null : "IN",
            value: phoneNumber,
            dialCode: hasCountryCode ? null : "+91",
            primary: false,
          },
        });
      }
      if (linkedInUrl) {
        payload.push({
          op: "add",
          path: "/linkedIn",
          value: linkedInUrl,
        });
      }
      if (companyName) {
        payload.push({
          op: "add",
          path: "/companyName",
          value: companyName,
        });
      }

      chrome.runtime.sendMessage(
        {
          event: "updateLead",
          data: {
            id: leadId,
            payload,
          },
        },
        async (response) => {
          if (response.success) {
            document.getElementById("updateLeadId").value = null;
            document.getElementById("updateFirstName").value = null;
            document.getElementById("updateLastName").value = null;
            document.getElementById("updateEmail").value = null;
            document.getElementById("updatePhoneNumber").value = null;
            document.getElementById("updateLinkedInUrl").value = null;
            document.getElementById("updateCompanyName").value = null;
            showToast(response.message, "success");
          } else if (
            response.code &&
            (response.code === "001002" ||
              response.code === "001003" ||
              response.code === "001004")
          ) {
            await setupToLogout();
            showToast(
              "Session expired or invalid. Please log in again.",
              "warning"
            );
          } else {
            const errorMessage = await getErrorMessage(response.code);
            showToast(errorMessage, "error");
          }
        }
      );
    });
};

const setupHomeEventListeners = () => {
  minimizeBtnListener();
  profilePageListener();
  profileHandler();
  logoutHandler();
  radioBtnListener();
  leadSearchHandler();

  document.addEventListener("click", (e) => {
    const updateSearchResults = document.getElementById("updateSearchResults");
    updateSearchResults.style.display = "none";
  });

  createLeadListener();
  updateLeadListener();
  emailListener();
};

const initializeApp = async () => {
  if (document.readyState !== "complete") {
    await new Promise((resolve) => window.addEventListener("load", resolve));
  }

  const toggleSidebarContainer = await createToggleSidebarContainer();
  const sidebarContainer = await createSidebarContainer();

  let lastUrl = window.location.href;
  new MutationObserver(() => {
    const currentUrl = window.location.href;
    if (currentUrl !== lastUrl) {
      if (!lastUrl.includes("/overlay") && !currentUrl.includes("/overlay")) {
        document.getElementById("firstName").value = null;
        document.getElementById("lastName").value = null;
        document.getElementById("email").value = null;
        document.getElementById("phoneNumber").value = null;
        document.getElementById("linkedInUrl").value = null;
        document.getElementById("companyName").value = null;
        document.getElementById("updateLeadId").value = null;
        document.getElementById("updateFirstName").value = null;
        document.getElementById("updateLastName").value = null;
        document.getElementById("updateEmail").value = null;
        document.getElementById("updatePhoneNumber").value = null;
        document.getElementById("updateLinkedInUrl").value = null;
        document.getElementById("updateCompanyName").value = null;
      }
      lastUrl = currentUrl;
      profilePageListener();
    }
  }).observe(document.body, { subtree: true, childList: true });

  setupEventListeners(sidebarContainer, toggleSidebarContainer);

  watchContactInfoButton();

  const tokenResponse = await chrome.storage.local.get(["token"]);
  if (tokenResponse.token) {
    setupHomePage();
  } else {
    loginHandler();
  }
};

initializeApp();
