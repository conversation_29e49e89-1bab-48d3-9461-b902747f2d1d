.toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  z-index: 10002;
  animation: slideIn 0.3s ease-in-out;
  max-width: 280px;
  width: calc(100% - 40px);
  box-sizing: border-box;
  word-wrap: break-word;
  line-height: 1.4;
}

.toast a {
  color: white;
  text-decoration: underline;
}

.toast.success {
  background-color: #28a645;
}

.toast.error {
  background-color: #ee0000;
}

.toast.warning {
  background-color: #fff3cd;
  color: black;
}

.toast.fade-out {
  animation: slideOut 0.3s ease-in-out forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
