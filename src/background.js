import decodeJwt from "./utils/decodeJwt.js";
import loginToKylas from "./api/loginToKylas.js";
import createLeadInKylas from "./api/createLeadInKylas.js";
import logoutFromKylas from "./api/logoutFromKylas.js";
import searchLeadInKylas from "./api/searchLeadInKylas.js";
import updateLeadInKylas from "./api/updateLeadInKylas.js";
import fetchLeadFields from "./api/fetchLeadFields.js";
import fetchLeadLabels from "./api/fetchLeadLabels.js";

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  const { event, data } = message;

  (async () => {
    const tokenResponse = await chrome.storage.local.get(["token"]);
    const token = tokenResponse.token;
    const accessToken = decodeJwt(token)?.data?.accessToken;

    switch (event) {
      case "login":
        const loginResponse = await loginTo<PERSON>ylas(data.payload);
        sendResponse(loginResponse);
        break;
      case "logout":
        const logoutResponse = await logoutFromKylas({ token: accessToken });
        sendResponse(logoutResponse);
        break;
      case "createLead":
        const createLeadResponse = await createLeadInKylas(data.payload, accessToken);
        sendResponse(createLeadResponse);
        break;
      case "searchLead":
        const searchLeadResponse = await searchLeadInKylas(data.payload, accessToken);
        sendResponse(searchLeadResponse);
        break;
      case "updateLead":
        const updateLeadResponse = await updateLeadInKylas(data.id, data.payload, accessToken);
        sendResponse(updateLeadResponse);
        break;
      case "fetchLeadFields":
        // First try to get from storage
        const cachedFields = await chrome.storage.local.get(["leadFields"]);
        if (cachedFields.leadFields) {
          sendResponse({
            success: true,
            data: cachedFields.leadFields
          });
        } else {
          // If not in storage, fetch from API
          const response = await fetchLeadFields(accessToken);
          sendResponse(response);
        }
        break;
      case "fetchLeadLabels":
        // First try to get from storage
        const cachedLabels = await chrome.storage.local.get(["leadLabels"]);
        if (cachedLabels.leadLabels) {
          sendResponse({
            success: true,
            data: cachedLabels.leadLabels
          });
        } else {
          // If not in storage, fetch from API
          const response = await fetchLeadLabels(accessToken);
          sendResponse(response);
        }
        break;
      default:
        sendResponse({ success: false, message: "Unknown event" });
        break;
    }
  })();

  return true;
});
