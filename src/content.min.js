(()=>{var I=async e=>{try{return await(await fetch(chrome.runtime.getURL(e))).text()}catch(t){return console.error(`Error loading ${e}:`,t),""}},g=(e,t,n)=>{let a=e.querySelector(t);a&&(a.src=chrome.runtime.getURL(n))},c=(e,t="success")=>{let n=document.createElement("div");n.className=`toast ${t}`,n.innerHTML=e,document.body.appendChild(n),setTimeout(()=>{n.classList.add("fade-out"),setTimeout(()=>{document.body.removeChild(n)},300)},4e3)},H=async()=>{let e=document.createElement("div");return e.id="toggleSidebarContainer",e.style.right="0px",e.innerHTML=await I("ui/components/toggleBtn.html"),g(e,"#kylas-logo","assets/kylas.png"),document.body.appendChild(e),e},$=async()=>{let e=document.createElement("div");return e.id="sidebarContainer",e.style.right="-320px",e.innerHTML=await I("ui/components/sidebar.html"),g(e,"#minimize","assets/minimize.svg"),g(e,"#logout","assets/logout.svg"),document.body.appendChild(e),e},A=async()=>{await new Promise(p=>setTimeout(p,3e3));let e=document.querySelector('[aria-labelledby="pv-contact-info"]');if(!e||e.style.display==="none")return null;let t=e.innerText,n=t.split(`
`)?.map(p=>p.trim()).filter(Boolean),a=n[1].trim().split(" ")||"",l=a[0],o=a[a.length-1],d=window.location.href.replace("/overlay/contact-info/",""),s=n.indexOf("Email"),m=s>=0&&n[s+1]?n[s+1]:"",i=t.match(/Phone\s+(.+?)\s+\(Mobile\)/),r=i?i[1].trim():"",E=document.querySelector('button[aria-label^="Current company:"]')?.getAttribute("aria-label")?.split(":")[1]?.replace(". Click to skip to experience card","")?.trim()||"";return{firstName:l,lastName:o,email:m,phoneNumber:r,linkedInUrl:d,companyName:E}},w=(e,t)=>{t.addEventListener("click",()=>{let n=e.style.right==="0px";e.style.right=n?"-320px":"0px"})},C=()=>{document.getElementById("minimizeBtn")?.addEventListener("click",()=>{document.getElementById("sidebarContainer").style.right="-320px",document.getElementById("toggleSidebarContainer").style.right="0px"})},N=()=>{let e=document.getElementById("updateSearchResults");e.innerHTML='<div class="no-results"><img src="https://assets.kylas.io/images/webloader.gif" alt="Loading..." class="kl-loading-gif"/div>',document.getElementById("updateSearchResults").style.display="block"},z=async()=>{let e=document.getElementById("updateSearchResults"),t=await L(!0),n=await L(!1);e.innerHTML=`
    <div class="no-results">
      <div>No matching ${t}</div>
      <button class="create-new-btn">Create New ${n}</button>
    </div>
  `,e.style.display="block",B(!0),e.querySelector(".create-new-btn")?.addEventListener("click",()=>{let l=document.getElementById("createLeadRadioBtn");l&&(l.checked=!0,l.dispatchEvent(new Event("change")),B(!1))})},B=e=>{let t=document.getElementById("updateLeadFormLastName"),n=document.getElementById("updateLeadFormEmail"),a=document.getElementById("updateLeadFormPhoneNumber"),l=document.getElementById("updateLeadFormLinkedInURL"),o=document.getElementById("updateLeadFormCompanyName"),d=document.getElementById("updateLeadId"),s=document.getElementById("updateLeadBtn");d&&d.value?(t.classList.remove("blur-container"),n.classList.remove("blur-container"),a.classList.remove("blur-container"),l.classList.remove("blur-container"),o.classList.remove("blur-container"),s.classList.remove("blur-container")):e?(t.classList.add("blur-container"),n.classList.add("blur-container"),a.classList.add("blur-container"),l.classList.add("blur-container"),o.classList.add("blur-container"),s.classList.add("blur-container")):(t.classList.remove("blur-container"),n.classList.remove("blur-container"),a.classList.remove("blur-container"),l.classList.remove("blur-container"),o.classList.remove("blur-container"),s.classList.remove("blur-container"))},S=()=>{window.location.href.includes("/in/")&&new MutationObserver((t,n)=>{let a=document.getElementById("top-card-text-details-contact-info");a&&(a.addEventListener("click",async()=>{let{firstName:l,lastName:o,email:d,phoneNumber:s,linkedInUrl:m,companyName:i}=await A();document.getElementById("firstName").value=l,document.getElementById("lastName").value=o,document.getElementById("email").value=d,document.getElementById("phoneNumber").value=s,document.getElementById("linkedInUrl").value=m,document.getElementById("companyName").value=i;let r=document.getElementById("updateLeadRadioBtn");k(),document.getElementById("updateLeadId").value=null,document.getElementById("updateFirstName").value=null,document.getElementById("updateLastName").value=null,document.getElementById("updateEmail").value=null,document.getElementById("updatePhoneNumber").value=null,document.getElementById("updateLinkedInUrl").value=null,document.getElementById("updateCompanyName").value=null}),n.disconnect())}).observe(document.body,{childList:!0,subtree:!0})},x=(e,t)=>{w(e,t),C()},q=e=>{let t={firstName:["firstName","updateFirstName"],lastName:["lastName","updateLastName"],emails:["email","updateEmail"],phoneNumbers:["phoneNumber","updatePhoneNumber"],linkedIn:["linkedInUrl","updateLinkedInUrl"],companyName:["companyName","updateCompanyName"]};e.forEach(n=>{if(t[n.name]){let[a,l]=t[n.name],o=document.getElementById(a);if(o){let s=o.parentElement.querySelector(".field-label");s&&(s.textContent=n.displayName)}let d=document.getElementById(l);if(d){let s=d.parentElement.querySelector(".field-label");s&&(s.textContent=n.displayName)}}})},L=async(e=!1)=>{let t=await chrome.storage.local.get(["leadLabels"]);return t.leadLabels?e?t.leadLabels.displayNamePlural:t.leadLabels.displayName:e?"Leads":"Lead"},D=async()=>{let e=await L(),t=document.getElementById("createLeadBtn");t&&(t.textContent=`Create ${e}`);let n=document.getElementById("updateLeadBtn");n&&(n.textContent=`Update ${e}`)},O=async()=>{let e=document.getElementById("phoneNumberTooltipIcon"),t=document.getElementById("updatephoneNumberTooltipIcon"),n=document.getElementById("phoneNumberTooltip"),a=document.getElementById("updatePhoneNumberTooltip"),l=await chrome.runtime.sendMessage({event:"fetchLeadFields"}),o="the phone number";if(l.success){let d=l.data.find(s=>s.name==="phoneNumbers");d&&d.displayName&&(o=d.displayName)}n.textContent=`Please enter ${o} along with country code`,e.addEventListener("mouseenter",()=>{n.style.display="block",n.style.top=`${e.offsetTop+e.offsetHeight+5}px`,n.style.left=`${e.offsetLeft}px`}),e.addEventListener("mouseleave",()=>{n.style.display="none"}),t.addEventListener("mouseenter",()=>{a.style.display="block",a.style.top=`${t.offsetTop+t.offsetHeight+5}px`,a.style.left=`${t.offsetLeft}px`}),t.addEventListener("mouseleave",()=>{a.style.display="none"})},F=async()=>{let e=await I("ui/components/home.html"),t=document.getElementById("sidebarContainer");t.innerHTML=e,chrome.runtime.sendMessage({event:"fetchLeadFields"},async o=>{if(o.success){let d=o.data;await D(),q(d)}else if(o.code&&(o.code==="001002"||o.code==="001003"||o.code==="001004"))await v(),c("Session expired or invalid. Please log in again.","warning");else{let d=await h(o.code);c(d,"error")}});let a=(await chrome.storage.local.get(["email"])).email,l=document.getElementById("userEmail");l.textContent=a,g(t,"#minimize","assets/minimize.svg"),g(t,"#logout","assets/logout.svg"),g(t,"#phoneNumberTooltipIcon","assets/info-circle.svg"),g(t,"#updatephoneNumberTooltipIcon","assets/info-circle.svg"),G(),O()},M=async()=>{let e=await chrome.storage.local.get("errorCodeMessages");if(e.errorCodeMessages)return e.errorCodeMessages;try{let t=await fetch("https://assets.kylas.io/chrome-extention/errorcode-messages.json");if(!t.ok)throw new Error("Failed to fetch error code messages");let n=await t.json();return await chrome.storage.local.set({errorCodeMessages:n}),n}catch(t){return console.error("Error fetching error code messages:",t),{}}},h=async e=>(await M())[e]||"Something went wrong. Please try again.",T=async()=>{await M(),document.getElementById("loginBtn")?.addEventListener("click",async()=>{let e=document.getElementById("email").value,t=document.getElementById("password").value;if(!e){c("Email is required","error");return}if(!f(e)){c("Please enter a valid email address","error");return}if(!t){c("Password is required","error");return}chrome.runtime.sendMessage({event:"login",data:{payload:{email:e,password:t,rememberMe:!0}}},async n=>{if(n.success)chrome.storage.local.set({token:n.data.token}),chrome.storage.local.set({email:e}),chrome.runtime.sendMessage({event:"fetchLeadLabels"},a=>{a.success||console.error("Failed to fetch lead labels:",a.message)}),c(n.message,"success"),F();else{let a=await h(n.code);c(a,"error")}})}),U()},v=async()=>{chrome.storage.local.remove("token"),chrome.storage.local.remove("email"),chrome.storage.local.remove("leadLabels"),chrome.storage.local.remove("leadFields"),chrome.storage.local.remove("errorCodeMessages");let e=document.getElementById("sidebarContainer"),t=document.getElementById("toggleSidebarContainer");e.innerHTML=await I("ui/components/sidebar.html"),g(e,"#minimize","assets/minimize.svg"),x(e,t),w(e,t),T()},b=async()=>{document.getElementById("logoutBtn")?.addEventListener("click",()=>{chrome.runtime.sendMessage({event:"logout",data:{}},async e=>{c(e.message,"success"),await v()})})},_=()=>{let e=new MutationObserver(t=>{let n=document.getElementById("top-card-text-details-contact-info");n&&!n.hasAttribute("listener-attached")&&(n.setAttribute("listener-attached","true"),n.addEventListener("click",()=>{let a=document.getElementById("updateLeadForm"),l=document.getElementById("updateLeadRadioBtn");l&&(l.checked=!0,l.dispatchEvent(new Event("change"))),setTimeout(()=>{N()},100)}))});return e.observe(document.body,{childList:!0,subtree:!0}),()=>e.disconnect()},j=()=>{let e=document.querySelectorAll('input[name="leadAction"]'),t=document.getElementById("createLeadForm"),n=document.getElementById("updateLeadForm");e.forEach(a=>{a.addEventListener("change",l=>{l.target.value==="create"?(t.style.display="block",n.style.display="none"):(N(),k(),t.style.display="none",n.style.display="block")})})},K=()=>{let e=document.getElementById("profileBtn"),t=document.getElementById("profileDropdown"),n=document.getElementById("userEmail"),a=document.querySelector(".profile-circle"),l=document.querySelector(".profile-circle-small");if(a&&l&&n){let d=n.textContent.charAt(0).toUpperCase();a.textContent=d,l.textContent=d}e?.addEventListener("click",o=>{o.stopPropagation(),t.style.display=t.style.display==="block"?"none":"block"}),document.addEventListener("click",o=>{!e?.contains(o.target)&&!t?.contains(o.target)&&(t.style.display="none",B(!1))})},R="",P="",k=()=>{let e,t=document.getElementById("firstName"),n=document.getElementById("email"),a=document.getElementById("phoneNumber"),l=document.getElementById("updateFirstName"),o=document.getElementById("updateSearchResults"),d=document.getElementById("updateLeadRadioBtn"),s=()=>{let m="";if(l.value.trim()!==""?m=l.value.trim():n.value&&n.value.trim()!==""?m=n.value.trim():a.value&&a.value.trim()!==""?m=a.value.trim():m=t.value.trim(),clearTimeout(e),m.length<=3){o.style.display="none";return}e=setTimeout(()=>{N(),chrome.runtime.sendMessage({event:"searchLead",data:{payload:{fields:["firstName","lastName","id","emails","phoneNumbers","linkedIn","companyName"],jsonRule:{rules:[{id:"multi_field",field:"multi_field",type:"multi_field",input:"multi_field",operator:"multi_field",value:m}],condition:"AND",valid:!0}}}},async i=>{let r=document.getElementById("updateSearchResults");i.success&&i.data?.content?.length>0?(r.innerHTML=i.data.content.map(p=>`
                  <div class="search-result-item" data-lead-id="${p.id}">
                    <div class="lead-name">${p.firstName?p.firstName:""} ${p.lastName?p.lastName:""} (#${p.id})</div>
                    <div class="lead-details">
                      ${p.emails?.[0]?.value||"No email"}
                    </div>
                  </div>
                `).join(""),r.querySelectorAll(".search-result-item").forEach(p=>{p.addEventListener("click",()=>{let u=i.data.content.find(y=>y.id===parseInt(p.dataset.leadId));if(u){R=u.emails?u?.emails[0]?.value:"",P=u.phoneNumbers?u?.phoneNumbers[0]?.value:"",document.getElementById("updateLeadId").value=u.id,u.firstName?document.getElementById("updateFirstName").value=u.firstName:document.getElementById("updateFirstName").value=document.getElementById("firstName").value,document.getElementById("updateLastName").value=u.lastName||"",u?.emails?document.getElementById("updateEmail").value=u?.emails[0]?.value:document.getElementById("updateEmail").value=document.getElementById("email").value,u?.phoneNumbers?document.getElementById("updatePhoneNumber").value=u?.phoneNumbers[0]?.value:document.getElementById("updatePhoneNumber").value=document.getElementById("phoneNumber").value,document.getElementById("linkedInUrl").value?document.getElementById("updateLinkedInUrl").value=document.getElementById("linkedInUrl").value:document.getElementById("updateLinkedInUrl").value=u.linkedIn,u.companyName?document.getElementById("updateCompanyName").value=u.companyName:document.getElementById("updateCompanyName").value=document.getElementById("companyName").value,r.style.display="none",document.addEventListener("click",Q=>{r.style.display="none"});let y="";document.getElementById("updateFirstName").value===""&&(y+="First Name, "),document.getElementById("updateLastName").value===""&&(y+="Last Name, "),document.getElementById("updateEmail").value===""&&(y+="Email, "),document.getElementById("updatePhoneNumber").value===""&&(y+="Phone Number, "),document.getElementById("updateLinkedInUrl").value===""&&(y+="LinkedIn URL, "),document.getElementById("updateCompanyName").value===""&&(y+="Company Name, "),y!==""&&(y+="These fields are not available in Kylas & LinkedIn",c(y,"warning"))}})})):i.code&&(i.code==="001002"||i.code==="001003"||i.code==="001004")?(console.log(i),await v(),c("Session expired or invalid. Please log in again.","warning")):z()})},300)};d?.addEventListener("click",s),l?.addEventListener("input",s),s()},f=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),U=()=>{["email","updateEmail"].forEach(e=>{document.getElementById(e)?.addEventListener("input",n=>{let a=n.target.value;a?f(a)?n.target.classList.remove("invalid-input"):n.target.classList.add("invalid-input"):n.target.classList.remove("invalid-input")})})},Z=()=>{document.getElementById("createLeadBtn")?.addEventListener("click",async()=>{if(!(await chrome.storage.local.get(["token"])).token){b();return}let t=document.getElementById("firstName").value,n=document.getElementById("lastName").value,a=document.getElementById("email").value,l=document.getElementById("phoneNumber").value.trim(),o=document.getElementById("linkedInUrl").value,d=document.getElementById("companyName").value;if(a&&!f(a)){c("Please enter a valid email address","error");return}if(!t.trim()&&!n.trim()){c("Can't create lead","error");return}let s=l.startsWith("+"),m=l?[{type:"MOBILE",code:s?null:"IN",value:l,dialCode:s?null:"+91",primary:!0}]:null;chrome.runtime.sendMessage({event:"createLead",data:{payload:{firstName:t,lastName:n,emails:a?[{type:"PERSONAL",value:a,primary:!0}]:null,phoneNumbers:m,linkedIn:o,companyName:d,utmSource:"LinkedIn"}}},async i=>{if(console.log(i),i.success)document.getElementById("firstName").value=null,document.getElementById("lastName").value=null,document.getElementById("email").value=null,document.getElementById("phoneNumber").value=null,document.getElementById("linkedInUrl").value=null,document.getElementById("companyName").value=null,c(i.message,"success");else if(i.code&&(i.code==="001002"||i.code==="001003"||i.code==="001004"))await v(),c("Session expired or invalid. Please log in again.","warning");else{let r=await h(i.code);c(r,"error")}})})},W=()=>{document.getElementById("updateLeadBtn")?.addEventListener("click",async()=>{if(!(await chrome.storage.local.get(["token"])).token){b();return}let t=document.getElementById("updateLeadId").value,n=document.getElementById("updateFirstName").value,a=document.getElementById("updateLastName").value,l=document.getElementById("updateEmail").value,o=document.getElementById("updatePhoneNumber").value.trim(),d=document.getElementById("updateLinkedInUrl").value,s=document.getElementById("updateCompanyName").value;if(l&&!f(l)){c("Please enter a valid email address","error");return}if(!t){c("No lead selected","error");return}let m=[];n&&m.push({op:"add",path:"/firstName",value:n}),a&&m.push({op:"add",path:"/lastName",value:a}),l&&l!=R&&m.push({op:"add",path:"/emails/0",value:{type:"PERSONAL",value:l,primary:!1}});let i=o.startsWith("+");o&&o!=P&&m.push({op:"add",path:"/phoneNumbers/0",value:{id:null,type:"MOBILE",code:i?null:"IN",value:o,dialCode:i?null:"+91",primary:!1}}),d&&m.push({op:"add",path:"/linkedIn",value:d}),s&&m.push({op:"add",path:"/companyName",value:s}),chrome.runtime.sendMessage({event:"updateLead",data:{id:t,payload:m}},async r=>{if(r.success)document.getElementById("updateLeadId").value=null,document.getElementById("updateFirstName").value=null,document.getElementById("updateLastName").value=null,document.getElementById("updateEmail").value=null,document.getElementById("updatePhoneNumber").value=null,document.getElementById("updateLinkedInUrl").value=null,document.getElementById("updateCompanyName").value=null,c(r.message,"success");else if(r.code&&(r.code==="001002"||r.code==="001003"||r.code==="001004"))await v(),c("Session expired or invalid. Please log in again.","warning");else{let E=await h(r.code);c(E,"error")}})})},G=()=>{C(),S(),K(),b(),j(),k(),document.addEventListener("click",e=>{let t=document.getElementById("updateSearchResults");t.style.display="none"}),Z(),W(),U()},J=async()=>{document.readyState!=="complete"&&await new Promise(l=>window.addEventListener("load",l));let e=await H(),t=await $(),n=window.location.href;new MutationObserver(()=>{let l=window.location.href;l!==n&&(!n.includes("/overlay")&&!l.includes("/overlay")&&(document.getElementById("firstName").value=null,document.getElementById("lastName").value=null,document.getElementById("email").value=null,document.getElementById("phoneNumber").value=null,document.getElementById("linkedInUrl").value=null,document.getElementById("companyName").value=null,document.getElementById("updateLeadId").value=null,document.getElementById("updateFirstName").value=null,document.getElementById("updateLastName").value=null,document.getElementById("updateEmail").value=null,document.getElementById("updatePhoneNumber").value=null,document.getElementById("updateLinkedInUrl").value=null,document.getElementById("updateCompanyName").value=null),n=l,S())}).observe(document.body,{subtree:!0,childList:!0}),x(t,e),_(),(await chrome.storage.local.get(["token"])).token?F():T()};J();})();
