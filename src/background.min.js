(()=>{var U=s=>{try{let[t,e,a]=s.split(".");return JSON.parse(atob(e))}catch(t){return console.log("Invalid JWT",t),null}},g=U;var T={backendBaseURL:"https://api.kylas.io",frontendBaseURL:"https://app.kylas.io"},r=T;var C=async s=>{let t=`${r.backendBaseURL}/v1/users/login`;try{let e=await fetch(t,{method:"PUT",headers:{"Content-Type":"application/json",Accept:"application/json","User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"},body:JSON.stringify(s)}),a=await e.json();return e.ok?{success:!0,message:"Logged In successfully !!",data:a}:{success:!1,message:a.message,code:a.code}}catch(e){return console.error("Lo<PERSON> failed",e),{success:!1,message:"Something went wrong. Please try again."}}},p=C;var I=async(s,t)=>{let e=`${r.backendBaseURL}/v1/leads`,o=(await chrome.storage.local.get(["leadLabels"])).leadLabels?.displayName||"Lead";try{let l=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${t}`,"User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"},body:JSON.stringify(s)}),n=await l.json();return l.ok?{success:!0,message:`${o} Created <a href="${r.frontendBaseURL}/sales/leads/details/${n.id}" target="_blank">(${o} ID : ${n.id})</a>`,data:n}:{success:!1,message:n.message,code:n.code}}catch(l){return console.error("Create lead failed",l),{success:!1,message:"Something went wrong. Please try again."}}},m=I;var K=async s=>{let t=`${r.backendBaseURL}/v1/tokens/logout`;try{let e=await fetch(t,{method:"DELETE",headers:{"Content-Type":"application/json",Accept:"application/json","User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"},body:JSON.stringify(s)}),a=await e.json();return e.ok?{success:!0,message:"Logged Out successfully !!"}:{success:!1,message:a.message,code:a.code}}catch(e){return console.error("Logout failed",e),{success:!1,message:"Something went wrong. Please try again."}}},f=K;var S=async(s,t)=>{let e=`${r.backendBaseURL}/v1/search/lead?sort=updatedAt,desc&page=0&size=10`;try{let a=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${t}`,"User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"},body:JSON.stringify(s)}),o=await a.json();return a.ok?{success:!0,message:"Search lead results fetched successfully",data:o}:{success:!1,message:o.message,code:o.code}}catch(a){return console.error("Search lead failed",a),{success:!1,message:"Something went wrong. Please try again."}}},y=S;var x=async(s,t,e)=>{let a=`${r.backendBaseURL}/v1/leads/${s}`,l=(await chrome.storage.local.get(["leadLabels"])).leadLabels?.displayName||"Lead";try{let n=await fetch(a,{method:"PATCH",headers:{"Content-Type":"application/json-patch+json",Accept:"application/json",Authorization:`Bearer ${e}`,"User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"},body:JSON.stringify(t)}),c=await n.json();return n.ok?{success:!0,message:`${l} Updated <a href="${r.frontendBaseURL}/sales/leads/details/${c.id}" target="_blank">(${l} ID : ${c.id})</a>`,data:c}:{success:!1,message:c.message,code:c.code}}catch(n){return console.error("Update lead failed",n),{success:!1,message:"Something went wrong. Please try again."}}},h=x;var B=async s=>{let t=`${r.backendBaseURL}/v1/entities/lead/fields?entityType=lead&custom-only=false`;try{let e=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${s}`,"User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"}}),a=await e.json();return e.ok?(chrome.storage.local.set({leadFields:a}),{success:!0,message:"Fields fetched successfully",data:a}):{success:!1,message:a.message,code:a.code}}catch(e){return console.error("Get fields failed",e),{success:!1,message:"Something went wrong. Please try again."}}},L=B;var E=async s=>{let t=`${r.backendBaseURL}/v1/entities/label`;try{let e=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${s}`,"User-Agent":"Kylas-LinkedIn-Chrome-Extension/1.0"}}),a=await e.json();if(!e.ok)return{success:!1,message:a.message,code:a.code};if(a.LEAD){let o={displayName:a.LEAD.displayName,displayNamePlural:a.LEAD.displayNamePlural};chrome.storage.local.set({leadLabels:o})}return{success:!0,message:"Lead labels fetched successfully",data:a.LEAD||null}}catch(e){return console.error("Get lead labels failed",e),{success:!1,message:"Something went wrong. Please try again."}}},k=E;chrome.runtime.onMessage.addListener((s,t,e)=>{let{event:a,data:o}=s;return(async()=>{let n=(await chrome.storage.local.get(["token"])).token,c=g(n)?.data?.accessToken;switch(a){case"login":let w=await p(o.payload);e(w);break;case"logout":let b=await f({token:c});e(b);break;case"createLead":let A=await m(o.payload,c);e(A);break;case"searchLead":let $=await y(o.payload,c);e($);break;case"updateLead":let j=await h(o.id,o.payload,c);e(j);break;case"fetchLeadFields":let d=await chrome.storage.local.get(["leadFields"]);if(d.leadFields)e({success:!0,data:d.leadFields});else{let i=await L(c);e(i)}break;case"fetchLeadLabels":let u=await chrome.storage.local.get(["leadLabels"]);if(u.leadLabels)e({success:!0,data:u.leadLabels});else{let i=await k(c);e(i)}break;default:e({success:!1,message:"Unknown event"});break}})(),!0});})();
