import config from "../config.js";

const fetchLeadFields = async (accessToken) => {
  const url = `${config.backendBaseURL}/v1/entities/lead/fields?entityType=lead&custom-only=false`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "Kylas-LinkedIn-Chrome-Extension/1.0"
      },
    });

    const data = await response.json();
    if (!response.ok) {
      return { success: false, message: data.message, code: data.code };
    }

    // Cache the fields data in chrome storage
    chrome.storage.local.set({ leadFields: data });

    return {
      success: true,
      message: "Fields fetched successfully",
      data,
    };
  } catch (error) {
    console.error("Get fields failed", error);
    return {
      success: false,
      message: "Something went wrong. Please try again.",
    };
  }
};

export default fetchLeadFields;