import config from "../config.js";

const searchLeadInKylas = async (payload, accessToken) => {
  const url = `${config.backendBaseURL}/v1/search/lead?sort=updatedAt,desc&page=0&size=10`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "Kylas-LinkedIn-Chrome-Extension/1.0"
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    if (!response.ok) {
      return { success: false, message: data.message, code: data.code };
    }

    return {
      success: true,
      message: "Search lead results fetched successfully",
      data,
    };
  } catch (error) {
    console.error("Search lead failed", error);
    return {
      success: false,
      message: "Something went wrong. Please try again.",
    };
  }
};

export default searchLeadInKylas;
