import config from "../config.js";

const logoutFromKylas = async (payload) => {
  const url = `${config.backendBaseURL}/v1/tokens/logout`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": "Kylas-LinkedIn-Chrome-Extension/1.0"
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    if (!response.ok) {
      return { success: false, message: data.message, code: data.code };
    }

    return {
      success: true,
      message: "Logged Out successfully !!",
    };
  } catch (error) {
    console.error("Logout failed", error);
    return {
      success: false,
      message: "Something went wrong. Please try again.",
    };
  }
};

export default logoutFromKylas;
