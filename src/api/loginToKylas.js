import config from "../config.js";

const loginToKylas = async (payload) => {
  const url = `${config.backendBaseURL}/v1/users/login`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": "Kylas-LinkedIn-Chrome-Extension/1.0"
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    if (!response.ok) {
      return { success: false, message: data.message, code: data.code };
    }

    return {
      success: true,
      message: "Logged In successfully !!",
      data,
    };
  } catch (error) {
    console.error("Login failed", error);
    return {
      success: false,
      message: "Something went wrong. Please try again.",
    };
  }
};

export default loginTo<PERSON>ylas;
