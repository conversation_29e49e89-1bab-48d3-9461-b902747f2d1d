import config from "../config.js";

const fetchLeadLabels = async (accessToken) => {
    const url = `${config.backendBaseURL}/v1/entities/label`;

    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": `Bearer ${accessToken}`,
                "User-Agent": "Kylas-LinkedIn-Chrome-Extension/1.0"
            },
        });

        const data = await response.json();
        if (!response.ok) {
            return { success: false, message: data.message, code: data.code };
        }

        // Extract only LEAD data and store in chrome storage
        if (data.LEAD) {
            const leadLabels = {
                displayName: data.LEAD.displayName,
                displayNamePlural: data.LEAD.displayNamePlural
            };

            chrome.storage.local.set({ leadLabels });
        }

        return {
            success: true,
            message: "Lead labels fetched successfully",
            data: data.LEAD || null,
        };
    } catch (error) {
        console.error("Get lead labels failed", error);
        return {
            success: false,
            message: "Something went wrong. Please try again.",
        };
    }
};

export default fetchLeadLabels;