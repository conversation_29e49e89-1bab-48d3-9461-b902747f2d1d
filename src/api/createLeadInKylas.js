import config from "../config.js";

const createLeadInKylas = async (payload, accessToken) => {
  const url = `${config.backendBaseURL}/v1/leads`;
  const leadLabels = await chrome.storage.local.get(["leadLabels"]);
  const leadDisplayName = leadLabels.leadLabels?.displayName || "Lead";

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "Kylas-LinkedIn-Chrome-Extension/1.0"
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    if (!response.ok) {
      return { success: false, message: data.message, code: data.code };
    }

    return {
      success: true,
      message: `${leadDisplayName} Created <a href="${config.frontendBaseURL}/sales/leads/details/${data.id}" target="_blank">(${leadDisplayName} ID : ${data.id})</a>`,
      data
    };
  } catch (error) {
    console.error(`Create lead failed`, error);
    return {
      success: false,
      message: "Something went wrong. Please try again.",
    };
  }
};

export default createLeadInKylas;
