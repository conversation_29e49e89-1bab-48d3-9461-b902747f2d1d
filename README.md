# Kylas LinkedIn Extension

A secure Chrome extension that extracts contact information from LinkedIn profiles and seamlessly integrates with Kylas CRM for lead management.

## 🔒 Security & Privacy

**Important**: This extension prioritizes user privacy and security:
- **No sensitive data collection**: The extension does NOT read any sensitive information from your LinkedIn account
- **Contact info only**: Only extracts publicly available contact information when you explicitly click the "Contact info" link on LinkedIn profiles
- **Secure authentication**: Uses secure token-based authentication with Kylas CRM
- **Local storage**: All data is stored locally in your browser and transmitted securely to Kylas CRM only

## ✨ Features

### Core Functionality
- **Contact Information Extraction**: Automatically extracts contact details from LinkedIn profiles including:
  - First Name & Last Name
  - Email Address
  - Phone Number (with country code support)
  - LinkedIn Profile URL
  - Company Name

### CRM Integration
- **Kylas CRM Sync**: Seamlessly integrates with Kylas CRM system
- **Lead Creation**: Create new leads directly from LinkedIn profiles
- **Lead Updates**: Search and update existing leads in your CRM
- **Duplicate Detection**: Smart search to find existing leads before creating new ones
- **Custom Field Support**: Dynamically adapts to your Kylas CRM field configurations

### User Experience
- **Sidebar Interface**: Clean, non-intrusive sidebar that slides in from the right
- **Toggle Button**: Easy access with a floating Kylas logo button
- **Real-time Search**: Instant search for existing leads as you type
- **Toast Notifications**: Clear feedback for all actions
- **Responsive Design**: Works seamlessly across different screen sizes

## 🚀 Installation

### Prerequisites
- Google Chrome browser
- Active Kylas CRM account
- Node.js (for development/building)

### Development Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/kylas_linkedIn_scrapper.git
   cd kylas_linkedIn_scrapper
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the extension**:
   ```bash
   npm run build
   ```

4. **Package for distribution**:
   ```bash
   npm run package
   ```

### Chrome Extension Installation

1. **Load in Chrome**:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `src/bundle` folder

2. **Alternative - Install from ZIP**:
   - Use the generated `src/bundle.zip` file
   - Extract and load the folder in Chrome extensions

## 🛠️ Build System

The project uses **esbuild** for fast, efficient bundling and minification:

### Build Scripts

```bash
# Full build process
npm run build

# Build JavaScript files only
npm run build:js

# Build and minify CSS files
npm run build:css

# Copy assets (manifest, HTML, images)
npm run copy:assets

# Create distribution package
npm run package

# Clean build artifacts
npm run clean

# Development build and package
npm run dev
```

### Build Output
- **Minified JavaScript**: `background.min.js`, `content.min.js`
- **Minified CSS**: `main.min.css` (combines all CSS files)
- **Bundle Directory**: `src/bundle/` (ready for Chrome Web Store)
- **Distribution ZIP**: `src/bundle.zip`

## 📁 Project Structure

```
kylas_linkedIn_scrapper/
├── src/
│   ├── api/                    # API integration modules
│   │   ├── createLeadInKylas.js
│   │   ├── fetchLeadFields.js
│   │   ├── fetchLeadLabels.js
│   │   ├── loginToKylas.js
│   │   ├── logoutFromKylas.js
│   │   ├── searchLeadInKylas.js
│   │   └── updateLeadInKylas.js
│   ├── assets/                 # Static assets
│   │   ├── kylas.png
│   │   ├── minimize.svg
│   │   ├── logout.svg
│   │   └── info-circle.svg
│   ├── ui/                     # User interface components
│   │   ├── components/
│   │   │   ├── home.html
│   │   │   ├── sidebar.html
│   │   │   └── toggleBtn.html
│   │   └── css/
│   │       ├── home.css
│   │       ├── sidebar.css
│   │       ├── toast.css
│   │       └── toggleBtn.css
│   ├── utils/                  # Utility functions
│   │   └── decodeJwt.js
│   ├── background.js           # Service worker
│   ├── content.js              # Content script
│   ├── config.js               # Configuration
│   ├── main.js                 # Entry point
│   ├── manifest.json           # Extension manifest
│   └── bundle/                 # Built extension (generated)
├── package.json                # Node.js dependencies and scripts
└── README.md                   # This file
```

## 🎯 How to Use

### First Time Setup

1. **Install the Extension**: Follow the installation steps above
2. **Open LinkedIn**: Navigate to any LinkedIn profile
3. **Login to Kylas**: Click the Kylas logo button and enter your Kylas CRM credentials

### Extracting Contact Information

1. **Navigate to LinkedIn Profile**: Go to any LinkedIn profile page
2. **Click Contact Info**: Click the "Contact info" link on the LinkedIn profile
3. **Automatic Extraction**: The extension automatically extracts available contact information
4. **Access Extension**: Click the Kylas logo button to open the sidebar

### Creating New Leads

1. **Select "Create new"**: Choose the create option in the extension sidebar
2. **Review Information**: Verify the auto-filled contact information
3. **Add Missing Details**: Fill in any missing information
4. **Create Lead**: Click "Create Lead" to save to your Kylas CRM

### Updating Existing Leads

1. **Select "Update existing"**: Choose the update option in the extension sidebar
2. **Search for Lead**: Type in the first name to search for existing leads
3. **Select Lead**: Choose the correct lead from the search results
4. **Update Information**: Modify any fields as needed
5. **Save Changes**: Click "Update Lead" to save changes

## 🔧 Configuration

### Kylas CRM Settings

The extension automatically adapts to your Kylas CRM configuration:
- **Custom Field Labels**: Dynamically loads your custom field names
- **Lead Terminology**: Uses your organization's lead terminology (e.g., "Contact", "Prospect")
- **Field Validation**: Respects your CRM's field validation rules

### Phone Number Format

- **Country Code Support**: Automatically detects and handles country codes
- **Default Country**: Defaults to India (+91) if no country code is provided
- **International Format**: Supports international phone number formats

## 🔍 Technical Details

### Architecture

- **Manifest V3**: Built using the latest Chrome Extension Manifest V3
- **Service Worker**: Background processing using modern service worker architecture
- **Content Scripts**: Secure content script injection for LinkedIn integration
- **ES Modules**: Modern JavaScript module system throughout

### Security Features

- **Token-based Authentication**: Secure JWT token authentication with Kylas CRM
- **HTTPS Only**: All API communications use HTTPS encryption
- **Local Storage**: Sensitive data stored securely in Chrome's local storage
- **Permission Scoping**: Minimal required permissions for enhanced security

### Performance

- **Lazy Loading**: Components loaded on-demand for optimal performance
- **Caching**: Intelligent caching of CRM field configurations
- **Minification**: All assets minified for faster loading
- **Bundle Optimization**: Optimized bundle size using esbuild

## 🌐 Browser Compatibility

- **Chrome**: Fully supported (Manifest V3)
- **Edge**: Compatible with Chromium-based Edge
- **Other Browsers**: Not currently supported (Chrome extension specific)

## 📋 Permissions Explained

The extension requests the following permissions:

- **activeTab**: Access to the current LinkedIn tab for contact extraction
- **tabs**: Tab management for navigation
- **scripting**: Inject content scripts into LinkedIn pages
- **storage**: Store authentication tokens and preferences locally
- **webRequest**: Monitor network requests for LinkedIn integration
- **declarativeNetRequest**: Handle network request modifications

### Host Permissions

- **https://www.linkedin.com/***: Access LinkedIn pages for contact extraction
- **https://*.kylas.io/***: Communicate with Kylas CRM API

## 🚨 Troubleshooting

### Common Issues

1. **Login Issues**:
   - Verify your Kylas CRM credentials
   - Check internet connection
   - Clear extension storage and try again

2. **Contact Info Not Extracting**:
   - Ensure you clicked the "Contact info" link on LinkedIn
   - Wait for the contact modal to fully load
   - Refresh the page and try again

3. **Extension Not Loading**:
   - Check if extension is enabled in Chrome
   - Verify permissions are granted
   - Try reloading the extension

### Debug Mode

For developers:
1. Open Chrome DevTools
2. Check Console for error messages
3. Inspect Network tab for API call issues
4. Use Chrome Extension DevTools for debugging

## 🤝 Contributing

1. **Fork the Repository**: Create your own fork
2. **Create Feature Branch**: `git checkout -b feature/amazing-feature`
3. **Commit Changes**: `git commit -m 'Add amazing feature'`
4. **Push to Branch**: `git push origin feature/amazing-feature`
5. **Open Pull Request**: Submit your changes for review

### Development Guidelines

- Follow existing code style and conventions
- Add comments for complex functionality
- Test thoroughly before submitting
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **GitHub Issues**: [Create an issue](https://github.com/your-username/kylas_linkedIn_scrapper/issues)
- **Kylas Support**: Contact your Kylas CRM administrator
- **Documentation**: Refer to this README and inline code comments

## 🔄 Version History

### v1.0.0
- Initial release
- Contact information extraction from LinkedIn
- Kylas CRM integration
- Lead creation and update functionality
- Secure authentication system

---

**Note**: This extension is designed to work exclusively with Kylas CRM and requires valid Kylas credentials for operation.